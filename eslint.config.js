import { defineConfig, globalIgnores } from 'eslint/config'
import js from '@eslint/js'
import prettierPlugin from 'eslint-plugin-prettier'
import prettierConfig from 'eslint-config-prettier'

export default defineConfig([
  globalIgnores([
    '**/node_modules/',
    '**/vendor/',
    '**/public/',
    '**/coverage/',
    '**/tmp/',
    '**/assets/builds/'
  ]),
  {
    files: ['**/*.js'],
    plugins: {
      js,
      prettier: prettierPlugin
    },
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module'
    },
    extends: ['js/recommended', prettierConfig],
    rules: {
      'prettier/prettier': 'warn'
    }
  }
])
