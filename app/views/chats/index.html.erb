<div class="flex h-screen bg-gray-100">
  <!-- Sidebar -->
  <div class="w-64 bg-gray-800 text-white flex flex-col">
    <!-- New Chat Button -->
    <div class="p-4 border-b border-gray-700">
      <button onclick="document.getElementById('new-chat-modal').classList.remove('hidden')" class="w-full bg-white text-gray-800 rounded-md py-2 px-4 font-medium hover:bg-gray-200 transition cursor-pointer">
        + New Chat
      </button>
    </div>

    <!-- Chat List -->
    <div class="flex-1 overflow-y-auto">
      <div class="px-3 py-2 text-xs text-gray-400 uppercase">Your chats</div>
      <div class="space-y-1 px-3">
        <% @chats.each do |chat| %>
          <%= link_to chat_path(chat), class: "flex items-center px-3 py-2 text-sm rounded-md hover:bg-gray-700 transition" do %>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
            <%= chat.title %>
          <% end %>
        <% end %>
      </div>
    </div>

    <!-- User Section -->
    <div class="p-4 border-t border-gray-700">
      <div class="flex items-center">
        <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center text-sm font-medium">
          U
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium">Guest User</p>
          <p class="text-xs text-gray-400">Using cookies</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="flex-1 flex flex-col">
    <!-- Welcome Screen -->
    <div class="flex-1 flex items-center justify-center bg-white">
      <div class="max-w-2xl text-center px-4">
        <h1 class="text-4xl font-bold mb-6">Chat Lamma</h1>
        <p class="text-xl text-gray-600 mb-8">Select an agent below to start a new conversation</p>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <% @agents.each do |agent| %>
            <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-sm transition">
              <%= form_with(model: Chat.new, local: true) do |f| %>
                <%= f.hidden_field :agent_id, value: agent.id %>
                <div class="flex items-center mb-3">
                  <img src="<%= agent.avatar_url %>" alt="<%= agent.name %>" class="w-10 h-10 rounded-full">
                  <div class="ml-3">
                    <h3 class="font-medium"><%= agent.name %></h3>
                  </div>
                </div>
                <p class="text-sm text-gray-600 mb-4"><%= agent.description %></p>
                <%= f.submit "Chat with #{agent.name}", class: "w-full bg-gray-800 text-white rounded-md py-2 px-4 text-sm font-medium hover:bg-gray-700 transition cursor-pointer" %>
              <% end %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- New Chat Modal -->
<div id="new-chat-modal" onclick="if(event.target === this) this.classList.add('hidden')" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
  <div class="bg-white rounded-lg max-w-md w-full p-6">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-medium">Create New Chat</h3>
      <button onclick="document.getElementById('new-chat-modal').classList.add('hidden')" class="text-gray-400 hover:text-gray-500">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <%= form_with(model: Chat.new, local: true) do |f| %>
      <div class="mb-4">
        <%= f.label :title, "Chat Title", class: "block text-sm font-medium text-gray-700 mb-1" %>
        <%= f.text_field :title, class: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" %>
      </div>

      <div class="mb-4">
        <%= f.label :agent_id, "Select Agent", class: "block text-sm font-medium text-gray-700 mb-1" %>
        <%= f.select :agent_id, @agents.map { |a| [a.name, a.id] }, {}, class: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500" %>
      </div>

      <div class="flex justify-end">
        <button type="button" onclick="document.getElementById('new-chat-modal').classList.add('hidden')" class="mr-3 px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-500 cursor-pointer">
          Cancel
        </button>
        <%= f.submit "Create Chat", class: "px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 cursor-pointer" %>
      </div>
    <% end %>
  </div>
</div>
