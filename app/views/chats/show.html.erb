<div class="flex h-screen bg-gray-100">
  <!-- Sidebar -->
  <div class="w-64 bg-gray-800 text-white flex flex-col">
    <!-- New Chat Button -->
    <div class="p-4 border-b border-gray-700">
      <%= link_to "+ New Chat", chats_path, class: "block w-full text-center bg-white text-gray-800 rounded-md py-2 px-4 font-medium hover:bg-gray-200 transition" %>
    </div>

    <!-- Chat List -->
    <div class="flex-1 overflow-y-auto">
      <div class="px-3 py-2 text-xs text-gray-400 uppercase">Your chats</div>
      <div class="space-y-1 px-3">
        <% @chats.each do |chat| %>
          <%= link_to chat_path(chat), class: "flex items-center px-3 py-2 text-sm rounded-md #{chat == @chat ? 'bg-gray-700' : 'hover:bg-gray-700'} transition" do %>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
            </svg>
            <%= chat.title %>
          <% end %>
        <% end %>
      </div>
    </div>

    <!-- User Section -->
    <div class="p-4 border-t border-gray-700">
      <div class="flex items-center">
        <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center text-sm font-medium">
          U
        </div>
        <div class="ml-3">
          <p class="text-sm font-medium">Guest User</p>
          <p class="text-xs text-gray-400">Using cookies</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="flex-1 flex flex-col">
    <!-- Chat Header -->
    <div class="bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
      <div class="flex items-center">
        <img src="<%= @chat.agent.avatar_url %>" alt="<%= @chat.agent.name %>" class="w-8 h-8 rounded-full">
        <div class="ml-3">
          <h2 class="text-lg font-medium"><%= @chat.title %></h2>
          <p class="text-sm text-gray-500">Chatting with <%= @chat.agent.name %></p>
        </div>
      </div>

      <%= button_to chat_path(@chat), method: :delete, class: "text-gray-400 hover:text-red-500", data: { confirm: "Are you sure you want to delete this chat?" } do %>
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
      <% end %>
    </div>

    <!-- Messages -->
    <div class="flex-1 overflow-y-auto p-4 space-y-4" id="messages-container">
      <% if @messages.empty? %>
        <div class="flex justify-center items-center h-full">
          <div class="text-center text-gray-500">
            <p class="mb-2">No messages yet</p>
            <p>Start the conversation by sending a message below</p>
          </div>
        </div>
      <% else %>
        <% @messages.each do |message| %>
          <div class="flex <%= message.role == 'user' ? 'justify-end' : 'justify-start' %>">
            <div class="max-w-3/4 <%= message.role == 'user' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800' %> rounded-lg px-4 py-2 message-content">
              <%= markdown(message.content) %>
              <div class="text-xs <%= message.role == 'user' ? 'text-blue-200' : 'text-gray-500' %> mt-1">
                <%= message.created_at.strftime("%H:%M") %>
              </div>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>

    <!-- Message Input -->
    <div class="bg-white border-t border-gray-200 p-4">
      <%= form_with(model: [@chat, Message.new],
                    local: false,
                    data: { controller: "chat-form", action: "submit->chat-form#submit" },
                    class: "flex space-x-4") do |f| %>
        <%= f.text_area :content,
                        placeholder: "Type your message...",
                        rows: 1,
                        data: { "chat-form-target": "input", action: "keydown->chat-form#handleKeydown" },
                        class: "flex-1 resize-none border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" %>
        <%= f.submit "Send",
                     data: { "chat-form-target": "submit" },
                     class: "bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500" %>
      <% end %>
    </div>
  </div>
</div>

<script>
  // Action Cable setup for real-time messaging
  document.addEventListener('DOMContentLoaded', function() {
    const chatId = <%= @chat.id %>;
    const messagesContainer = document.getElementById('messages-container');

    // Subscribe to the chat channel
    const subscription = App.cable.subscriptions.create(
      { channel: 'ChatChannel', chat_id: chatId },
      {
        received: function(data) {
          if (data.type === 'message') {
            removeTypingIndicator();
            appendMessage(data.message);
            scrollToBottom();
          } else if (data.type === 'typing_start') {
            showTypingIndicator(data.agent_name);
            scrollToBottom();
          } else if (data.type === 'typing_stop') {
            removeTypingIndicator();
          }
        }
      }
    );

    function appendMessage(message) {
      const messageDiv = document.createElement('div');
      messageDiv.className = `flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`;

      // Render markdown content
      const markdownContent = window.marked ? window.marked.parse(message.content) : message.content.replace(/\n/g, '<br>');

      messageDiv.innerHTML = `
        <div class="max-w-3/4 ${message.role === 'user' ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-800'} rounded-lg px-4 py-2 message-content">
          <div>${markdownContent}</div>
          <div class="text-xs ${message.role === 'user' ? 'text-blue-200' : 'text-gray-500'} mt-1">
            ${message.created_at}
          </div>
        </div>
      `;

      // Remove empty state if it exists
      const emptyState = messagesContainer.querySelector('.flex.justify-center.items-center.h-full');
      if (emptyState) {
        emptyState.remove();
      }

      messagesContainer.appendChild(messageDiv);
    }

    function showTypingIndicator(agentName) {
      // Remove existing typing indicator if any
      removeTypingIndicator();

      const typingDiv = document.createElement('div');
      typingDiv.className = 'flex justify-start';
      typingDiv.id = 'typing-indicator';

      typingDiv.innerHTML = `
        <div class="max-w-3/4 typing-indicator">
          <div class="typing-dots">
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
          </div>
          <div class="typing-text">${agentName} is typing...</div>
        </div>
      `;

      // Remove empty state if it exists
      const emptyState = messagesContainer.querySelector('.flex.justify-center.items-center.h-full');
      if (emptyState) {
        emptyState.remove();
      }

      messagesContainer.appendChild(typingDiv);
    }

    function removeTypingIndicator() {
      const typingIndicator = document.getElementById('typing-indicator');
      if (typingIndicator) {
        typingIndicator.remove();
      }
    }

    function scrollToBottom() {
      messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // Initial scroll to bottom
    scrollToBottom();
  });
</script>
