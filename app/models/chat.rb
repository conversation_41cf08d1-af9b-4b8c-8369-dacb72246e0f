# frozen_string_literal: true

# == Schema Information
#
# Table name: chats
#
#  id          :bigint           not null, primary key
#  title       :string           not null
#  user_cookie :string           not null
#  created_at  :datetime         not null
#  updated_at  :datetime         not null
#  agent_id    :bigint           not null
#
# Indexes
#
#  index_chats_on_agent_id     (agent_id)
#  index_chats_on_user_cookie  (user_cookie)
#
# Foreign Keys
#
#  fk_rails_...  (agent_id => agents.id)
#
class Chat < ApplicationRecord
  belongs_to :agent
  has_many :messages, dependent: :destroy

  validates :title, presence: true

  before_validation :set_default_title

  private

  def set_default_title
    return if title.present?

    Rails.logger.debug { "Setting default title. Agent: #{agent&.name}, Agent ID: #{agent_id}" }

    if agent
      self.title = "Chat with #{agent.name}"
    elsif agent_id.present?
      # If agent is not loaded but agent_id is present, load it
      loaded_agent = Agent.find_by(id: agent_id)
      self.title = loaded_agent ? "Chat with #{loaded_agent.name}" : 'New Chat'
    else
      self.title = 'New Chat'
    end

    Rails.logger.debug { "Title set to: #{title}" }
  end
end
