import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static targets = ['modal']

  connect() {
    // Bind escape key to close modal
    this.boundEscapeHandler = this.handleEscape.bind(this)
    document.addEventListener('keydown', this.boundEscapeHandler)
  }

  disconnect() {
    document.removeEventListener('keydown', this.boundEscapeHandler)
  }

  open() {
    this.element.classList.remove('hidden')
    document.body.classList.add('overflow-hidden')
  }

  close() {
    this.element.classList.add('hidden')
    document.body.classList.remove('overflow-hidden')
  }

  handleEscape(event) {
    if (event.key === 'Escape') {
      this.close()
    }
  }

  // Close modal when clicking outside the modal content
  clickOutside(event) {
    if (event.target === this.element) {
      this.close()
    }
  }
}
