import { Controller } from '@hotwired/stimulus'
import { renderMarkdown, sanitizeMarkdown } from '../utils/markdown'

export default class extends Controller {
  static targets = ['input', 'submit']

  connect() {
    this.autoResize()
  }

  submit(event) {
    event.preventDefault()

    const content = this.inputTarget.value.trim()
    if (!content) return

    // Disable form while sending
    this.submitTarget.disabled = true
    this.inputTarget.disabled = true

    // Submit the form via fetch
    fetch(this.element.action, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': document.querySelector('[name="csrf-token"]').content
      },
      body: JSON.stringify({
        message: { content: content }
      })
    })
    .then(response => {
      if (response.ok) {
        // Clear the input
        this.inputTarget.value = ''
        this.autoResize()
      } else {
        console.error('Failed to send message')
      }
    })
    .catch(error => {
      console.error('Error sending message:', error)
    })
    .finally(() => {
      // Re-enable form
      this.submitTarget.disabled = false
      this.inputTarget.disabled = false
      this.inputTarget.focus()
    })
  }

  handleKeydown(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault()
      this.submit(event)
    }

    // Auto-resize textarea
    setTimeout(() => this.autoResize(), 0)
  }

  autoResize() {
    const textarea = this.inputTarget
    textarea.style.height = 'auto'
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
  }
}
