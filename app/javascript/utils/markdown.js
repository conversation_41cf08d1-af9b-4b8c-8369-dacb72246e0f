// Markdown utility functions for chat messages

export function renderMarkdown(content) {
  if (!content || typeof content !== 'string') {
    return ''
  }

  // Use the globally available marked instance
  if (window.marked) {
    return window.marked.parse(content)
  }

  // Fallback to simple line break conversion if marked is not available
  return content.replace(/\n/g, '<br>')
}

export function escapeHtml(text) {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

export function sanitizeMarkdown(html) {
  // Basic sanitization - remove script tags and dangerous attributes
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = html
  
  // Remove script tags
  const scripts = tempDiv.querySelectorAll('script')
  scripts.forEach(script => script.remove())
  
  // Remove dangerous attributes
  const allElements = tempDiv.querySelectorAll('*')
  allElements.forEach(element => {
    const dangerousAttrs = ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur']
    dangerousAttrs.forEach(attr => {
      if (element.hasAttribute(attr)) {
        element.removeAttribute(attr)
      }
    })
  })
  
  return tempDiv.innerHTML
}
