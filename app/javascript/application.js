// Entry point for the build script in your package.json
import '@hotwired/turbo-rails'
import './controllers'
import '@rails/actioncable'
import { marked } from 'marked'

// Configure marked for safe HTML rendering
marked.setOptions({
  breaks: true,
  gfm: true,
  sanitize: false, // We'll handle sanitization on the server side
  smartLists: true,
  smartypants: true
})

// Set up Action Cable
import { createConsumer } from '@rails/actioncable'
window.App = {}
App.cable = createConsumer()

// Make marked available globally for use in other scripts
window.marked = marked
