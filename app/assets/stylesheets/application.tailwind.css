@import "tailwindcss";

/* Markdown styling for chat messages */
.message-content h1,
.message-content h2,
.message-content h3,
.message-content h4,
.message-content h5,
.message-content h6 {
  font-weight: bold;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

.message-content h1 { font-size: 1.5rem; }
.message-content h2 { font-size: 1.25rem; }
.message-content h3 { font-size: 1.125rem; }
.message-content h4 { font-size: 1rem; }
.message-content h5 { font-size: 0.875rem; }
.message-content h6 { font-size: 0.75rem; }

.message-content p {
  margin-bottom: 0.75rem;
}

.message-content p:last-child {
  margin-bottom: 0;
}

.message-content ul,
.message-content ol {
  margin-bottom: 0.75rem;
  padding-left: 1.5rem;
}

.message-content ul {
  list-style-type: disc;
}

.message-content ol {
  list-style-type: decimal;
}

.message-content li {
  margin-bottom: 0.25rem;
}

.message-content blockquote {
  border-left: 4px solid #e5e7eb;
  padding-left: 1rem;
  margin: 1rem 0;
  font-style: italic;
}

.message-content code {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

.message-content pre {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

.message-content pre code {
  background-color: transparent;
  padding: 0;
}

.message-content strong {
  font-weight: bold;
}

.message-content em {
  font-style: italic;
}

.message-content a {
  color: #3b82f6;
  text-decoration: underline;
}

.message-content a:hover {
  color: #1d4ed8;
}

.message-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 1rem 0;
}

.message-content th,
.message-content td {
  border: 1px solid #e5e7eb;
  padding: 0.5rem;
  text-align: left;
}

.message-content th {
  background-color: rgba(0, 0, 0, 0.05);
  font-weight: bold;
}

.message-content hr {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 1.5rem 0;
}

/* Adjust styles for user messages (white background) */
.bg-blue-600 .message-content blockquote {
  border-left-color: rgba(255, 255, 255, 0.3);
}

.bg-blue-600 .message-content code {
  background-color: rgba(255, 255, 255, 0.2);
}

.bg-blue-600 .message-content pre {
  background-color: rgba(255, 255, 255, 0.2);
}

.bg-blue-600 .message-content th {
  background-color: rgba(255, 255, 255, 0.1);
}

.bg-blue-600 .message-content th,
.bg-blue-600 .message-content td {
  border-color: rgba(255, 255, 255, 0.3);
}

.bg-blue-600 .message-content hr {
  border-top-color: rgba(255, 255, 255, 0.3);
}

.bg-blue-600 .message-content a {
  color: #bfdbfe;
}

.bg-blue-600 .message-content a:hover {
  color: #dbeafe;
}

/* Typing indicator styles */
.typing-indicator {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: #f3f4f6;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.typing-dots {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.typing-dot {
  width: 0.5rem;
  height: 0.5rem;
  background-color: #6b7280;
  border-radius: 50%;
  animation: typing-bounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes typing-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.typing-text {
  margin-left: 0.75rem;
  color: #6b7280;
  font-style: italic;
  font-size: 0.875rem;
}
