#!/bin/bash
set -e

# Remove a potentially pre-existing server.pid for Rails
rm -f /rails/tmp/pids/server.pid

# Enable jemalloc for reduced memory usage and latency
if [ -z "${LD_PRELOAD+x}" ]; then
    LD_PRELOAD=$(find /usr/lib -name libjemalloc.so.2 -print -quit)
    export LD_PRELOAD
fi

# Install Ruby gems if needed
bundle check || bundle install

# Check if node packages need to be installed or updated
if [ -f "yarn.lock" ]; then
    if [ ! -d "node_modules" ]; then
        echo "Node modules directory not found. Installing packages..."
        yarn install
    else
        # Check if yarn.lock has changed since last install
        if [ ! -f "node_modules/.yarn-integrity" ] || ! yarn check --verify-tree 2>/dev/null; then
            echo "Node packages out of date. Updating..."
            yarn install
        else
            echo "Node packages up to date"
        fi
    fi
fi

# Then exec the container's main process (what's set as CMD in the Dockerfile)
exec "$@"
