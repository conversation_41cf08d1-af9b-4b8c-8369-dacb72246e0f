services:
  web:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - .:/rails
      - bundle_cache:/usr/local/bundle
      - node_modules:/rails/node_modules
    environment:
      - RAILS_ENV=development
      - DATABASE_URL=************************************/chat_lamma_development
    depends_on:
      - db
    stdin_open: true
    tty: true

  db:
    image: postgres:14
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5432:5432"

volumes:
  postgres_data:
  bundle_cache:
  node_modules:
