# Use this file to configure the Overcommit hooks you wish to use. This will
# extend the default configuration defined in:
# https://github.com/sds/overcommit/blob/master/config/default.yml
#
# At the topmost level of this YAML file is a key representing type of hook
# being run (e.g. pre-commit, commit-msg, etc.). Within each type you can
# customize each hook, such as whether to only run it on certain files (via
# `include`), whether to only display output if it fails (via `quiet`), etc.
#
# For a complete list of hooks, see:
# https://github.com/sds/overcommit/tree/master/lib/overcommit/hook
#
# For a complete list of options that you can use to customize hooks, see:
# https://github.com/sds/overcommit#configuration
#
# Uncomment the following lines to make the configuration take effect.

PreCommit:
  ALL:
    problem_on_unmodified_line: ignore
    exclude:
      - 'public/**/*'
      - 'node_modules/**/*'
      - 'vendor/**/*'
      - 'tmp/**/*'

  RuboCop:
    enabled: true
    on_warn: fail # Treat all warnings as failures

  TrailingWhitespace:
    enabled: true

  EsLint:
    enabled: true
    description: 'Analyze with ESLint'
    flags: ['--format=compact']
    required_executable: 'yarn'
    command: ['yarn', 'eslint', '--config', 'eslint.config.js']
    include: '**/*.js'

  StyleLint:
    enabled: true
    description: 'Check styles with Stylelint'
    command: ['yarn', 'stylelint', '**/*.css']
    include:
      - '**/*.css'

#PostCheckout:
#  ALL: # Special hook name that customizes all hooks of this type
#    quiet: true # Change all post-checkout hooks to only display output on failure
#
#  IndexTags:
#    enabled: true # Generate a tags file with `ctags` each time HEAD changes
